package com.frt.usercore.dao.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.frt.usercore.dao.entity.AccountDO;
import com.frt.usercore.dao.mapper.AccountMapper;
import com.frt.usercore.dao.repository.AccountDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 运营后台端用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class AccountDAOImpl extends ServiceImpl<AccountMapper, AccountDO> implements AccountDAO {

    @Override
    public AccountDO selectByAccountAndPlatformType(String account, Integer platformType,  String tenantId) {
        QueryWrapper<AccountDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tenant_id",tenantId)
                .eq("account", account)
                .eq("platform_type", platformType)
                .eq("is_del", 0);
        return getOne(queryWrapper);
    }
    @Override
    public AccountDO getByAccountAndPasswordAndPlatformId(String account, String password, Integer platformType) {
        LambdaQueryWrapper<AccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountDO::getAccount, account)
                   .eq(AccountDO::getPassword, password)
                   .eq(AccountDO::getPlatformType, platformType);
        return this.getOne(queryWrapper);
    }

    /**
     * 根据账号查询账户信息
     *
     * @param account 账号
     * @return 账户信息
     */
    @Override
    public AccountDO getByAccount(String account) {
        LambdaQueryWrapper<AccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountDO::getAccount, account)
                .eq(AccountDO::getIsDel, 0)
                .last("limit 1");
        return this.getOne(queryWrapper);
    }

    /**
     * 根据UserId查询账户信息
     *
     * @param userId 账号
     * @return 账户信息
     */
    @Override
    public AccountDO getByUserId(String userId) {
        LambdaQueryWrapper<AccountDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountDO::getUserId, userId)
                .eq(AccountDO::getIsDel, 0)
                .last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public boolean updateByUserId(AccountDO accountDO) {
        return this.baseMapper.updateByUserId(accountDO);
    }
}
