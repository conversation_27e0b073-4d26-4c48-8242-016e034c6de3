package com.frt.usercore.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.dao.entity.TenantRoleDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.frt.usercore.domain.dto.param.RoleListQueryParamDTO;
import com.frt.usercore.domain.param.PageParam;

/**
 * <p>
 * 租户角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface TenantRoleDAO extends IService<TenantRoleDO> {

    /**
     * 根据角色ID查询角色信息
     * @param roleId 角色ID
     * @return 角色信息
     */
    TenantRoleDO getByRoleId(String roleId);

	/**
	 * 分页查询角色列表
	 *
	 * @param pageDTO 分页参数
	 * @return 分页结果
	 */
	Page<TenantRoleDO> findPageList(PageParam<RoleListQueryParamDTO> pageDTO);

	/**
	 * 根据角色名称查询角色
	 * @param roleId 角色ID
	 * @param tenantId 租户ID
	 * @param roleType 角色类型 1-运营 2-商户
	 * @return 角色
	 */
	TenantRoleDO deleteByRoleId(String roleId, String tenantId, Integer roleType);
}
