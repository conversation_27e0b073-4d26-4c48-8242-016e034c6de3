package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.dao.entity.MerchantUserDO;
import com.frt.usercore.dao.mapper.MerchantUserMapper;
import com.frt.usercore.dao.repository.MerchantUserDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 运营后台端用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class MerchantUserDAOImpl extends ServiceImpl<MerchantUserMapper, MerchantUserDO> implements MerchantUserDAO {

    @Override
    public MerchantUserDO getByUserId(String userId) {
        return this.lambdaQuery()
                .eq(MerchantUserDO::getUserId, userId)
                .one();
    }

}
