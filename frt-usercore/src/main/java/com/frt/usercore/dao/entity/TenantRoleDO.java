package com.frt.usercore.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 租户角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Data
@TableName("frt_tenant_role")
public class TenantRoleDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色业务ID
     */
    @TableField("role_id")
    private String roleId;

    /**
     * 租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 商户ID
     */
    @TableField("merchant_id")
    private String merchantId;

    /**
     * 角色名称
     */
    @TableField("role_name")
    private String roleName;

    /**
     * 角色描述
     */
    @TableField("role_description")
    private String roleDescription;

    /**
     * 角色类型 1-运营 2-代理商 3-商户
     */
    @TableField("role_type")
    private Integer roleType;

    /**
     * 状态(0:禁用,1:启用)
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否是超管角色(0:不是,1:是)
     */
    @TableField("is_super_admin")
    private Integer isSuperAdmin;

    /**
     * 权限模版ID
     */
    @TableField("role_template_id")
    private String roleTemplateId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 逻辑删除标志(0:未删除, 1:已删除)
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String ROLE_ID = "role_id";

    public static final String TENANT_ID = "tenant_id";

    public static final String MERCHANT_ID = "merchant_id";

    public static final String ROLE_NAME = "role_name";

    public static final String ROLE_DESCRIPTION = "role_description";

    public static final String ROLE_TYPE = "role_type";

    public static final String STATUS = "status";

    public static final String IS_SUPER_ADMIN = "is_super_admin";

    public static final String ROLE_TEMPLATE_ID = "role_template_id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String CREATED_BY = "created_by";

    public static final String UPDATED_BY = "updated_by";

    public static final String IS_DEL = "is_del";

}
