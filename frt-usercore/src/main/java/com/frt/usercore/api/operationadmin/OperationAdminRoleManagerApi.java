package com.frt.usercore.api.operationadmin;

import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.RoleAddParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.RoleDetailQueryParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.RoleListQueryParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.RoleModifyParam;
import com.frt.usercore.domain.result.common.PageResult;
import com.frt.usercore.domain.result.operationadmin.rolemanager.RoleDetailQueryResult;
import com.frt.usercore.domain.result.operationadmin.rolemanager.RoleListQueryResult;
import com.frt.usercore.service.OperationAdminRoleManagerService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 运营后台角色管理API
 *
 * <AUTHOR>
 * @version OperationAdminRoleManagerApi.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/operation/admin/role")
public class OperationAdminRoleManagerApi {

    private final OperationAdminRoleManagerService operationAdminRoleManagerService;

    /**
     * 角色列表
     *
     * @param param 请求参数
     * @return 角色列表
     */
    @PostMapping("/role-list")
    public PageResult<RoleListQueryResult> getRoleList(@RequestBody PageParam<RoleListQueryParam> param) {
        return operationAdminRoleManagerService.getRoleList(param);
    }

    /**
     * 新增角色
     *
     * @param param 请求参数
     */
    @PostMapping("/role-add")
    public void addRole(@RequestBody RoleAddParam param) {
        operationAdminRoleManagerService.addRole(param);
    }

    /**
     * 修改角色
     *
     * @param param 请求参数
     */
    @PostMapping("/role-modify")
    public void modifyRole(@RequestBody RoleModifyParam param) {
        operationAdminRoleManagerService.modifyRole(param);
    }

    /**
     * 角色详情
     *
     * @param param 请求参数
     * @return 角色详情
     */
    @PostMapping("/role-detail")
    public RoleDetailQueryResult getRoleDetail(@RequestBody RoleDetailQueryParam param) {
        return operationAdminRoleManagerService.getRoleDetail(param);
    }
}
