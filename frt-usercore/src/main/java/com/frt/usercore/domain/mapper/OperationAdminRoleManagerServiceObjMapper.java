package com.frt.usercore.domain.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.dao.entity.TenantRoleDO;
import com.frt.usercore.domain.dto.param.RoleListQueryParamDTO;
import com.frt.usercore.domain.param.PageParam;
import com.frt.usercore.domain.param.operationadmin.rolemanager.RoleListQueryParam;
import com.frt.usercore.domain.result.common.PageResult;
import com.frt.usercore.domain.result.operationadmin.rolemanager.RoleListQueryResult;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface OperationAdminRoleManagerServiceObjMapper {

	PageParam<RoleListQueryParamDTO> toRoleListQueryParamDTOPage(PageParam<RoleListQueryParam> param);

	PageResult<RoleListQueryResult> toRoleListQueryResultPageResult(Page<TenantRoleDO> pageList);
}
