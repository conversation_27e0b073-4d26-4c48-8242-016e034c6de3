package com.frt.usercore.domain.param.operationadmin.rolemanager;

import lombok.Data;

/**
 * 角色列表查询参数
 *
 * <AUTHOR>
 * @version RoleListQueryParam.java, v 0.1 2025-08-28 15:00 tuyuwei
 */
@Data
public class RoleListQueryParam {

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 终端类型 1-商户端 2-运营端
     */
    private Integer terminalType;

    /**
     * 租户 id
     */
    private String tenantId;
}
