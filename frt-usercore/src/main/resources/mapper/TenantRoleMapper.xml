<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frt.usercore.dao.mapper.TenantRoleMapper">

    <select id="findPageList" resultType="com.frt.usercore.dao.entity.TenantRoleDO">
        SELECT * FROM frt_tenant_role WHERE
        tenant_id = #{query.tenantId, jdbcType=VARCHAR}
        <if test="query.roleName != null and query.roleName != ''">
            AND role_name LIKE CONCAT('%', #{query.roleName, jdbcType=VARCHAR}, '%')
        </if>
        and status = 1
        and role_type = #{query.terminalType, jdbcType=INTEGER}
    </select>
</mapper>
