<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frt.usercore.dao.mapper.AccountMapper">

    <update id="updateByUserId">
        update frt_account
        <set>
            <if test="account != null">
                account = #{account,jdbcType=VARCHAR},
            </if>
            <if test="password != null">
                password = #{password,jdbcType=VARCHAR},
            </if>
            <if test="salt != null">
                salt = #{salt,jdbcType=VARCHAR},
            </if>
            <if test="accountStatus != null">
                account_status = #{accountStatus},
            </if>
            <if test="name != ''">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="phone != ''">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
        </set>
        where user_id = #{userId}
    </update>

</mapper>
